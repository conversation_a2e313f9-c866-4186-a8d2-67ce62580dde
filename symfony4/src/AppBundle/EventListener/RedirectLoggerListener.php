<?php
namespace AppBundle\EventListener;

use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Psr\Log\LoggerInterface;

class RedirectLoggerListener
{
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function onKernelResponse(ResponseEvent $event)
    {
        $request = $event->getRequest();
        $response = $event->getResponse();

        if ($response instanceof RedirectResponse) {
            $this->logger->info('[REDIRECT] Scheme: ' . $request->getScheme());
            $this->logger->info('[REDIRECT] Host: ' . $request->getHost());
            $this->logger->info('[REDIRECT] X-Forwarded-Proto: ' . $request->headers->get('X-Forwarded-Proto'));
            $this->logger->info('[REDIRECT] Redirect URL: ' . $response->getTargetUrl());
        }
    }
}

